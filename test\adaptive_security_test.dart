import 'package:flutter_test/flutter_test.dart';
import 'package:trust_chain_banking/services/adaptive_security_monitor.dart';
import 'package:trust_chain_banking/services/gyro_tracker.dart';
import 'package:trust_chain_banking/services/hold_angle_tracker.dart';
import 'package:trust_chain_banking/services/background_anomaly_service.dart';
import 'package:trust_chain_banking/models/risk_level.dart';

void main() {
  group('AdaptiveSecurityMonitor Tests', () {
    late AdaptiveSecurityMonitor monitor;
    
    setUp(() {
      monitor = AdaptiveSecurityMonitor.instance;
      enableGyroTracking();
      GyroTracker().startPassive();
      HoldAngleTracker().start();
    });

    tearDown(() {
      monitor.stopMonitoring();
      GyroTracker().stopPassive();
      HoldAngleTracker().stop();
    });

    test('Monitoring starts and stops correctly', () async {
      bool riskLevelChanged = false;
      RiskLevel? lastRiskLevel;
      String? lastMessage;

      await monitor.startMonitoring(
        onRiskLevelChanged: (level, message) {
          riskLevelChanged = true;
          lastRiskLevel = level;
          lastMessage = message;
        },
        onForceLogout: () {},
      );

      // Wait for initial monitoring cycle
      await Future.delayed(const Duration(seconds: 35));

      expect(riskLevelChanged, true);
      expect(lastRiskLevel, isNotNull);
      expect(lastMessage, isNotNull);

      final stats = monitor.getMonitoringStats();
      expect(stats['is_monitoring'], true);
      expect(stats['current_risk_score'], isA<double>());
      expect(stats['anomaly_stats'], isNotNull);
    });

    test('High risk behavior triggers force logout', () async {
      bool logoutCalled = false;
      RiskLevel? lastRiskLevel;

      await monitor.startMonitoring(
        onRiskLevelChanged: (level, message) {
          lastRiskLevel = level;
        },
        onForceLogout: () {
          logoutCalled = true;
        },
      );

      // Simulate risky behavior
      for (int i = 0; i < 50; i++) {
        GyroTracker().consumeMagnitudes().add(10.0); // High movement
        HoldAngleTracker().consumeHoldAngles().add(90.0); // Extreme angle
      }

      // Wait for monitoring cycle
      await Future.delayed(const Duration(seconds: 35));

      expect(logoutCalled, true);
      expect(lastRiskLevel, RiskLevel.high);
    });

    test('Moderate risk triggers biometric prompt', () async {
      RiskLevel? lastRiskLevel;
      String? lastMessage;

      await monitor.startMonitoring(
        onRiskLevelChanged: (level, message) {
          lastRiskLevel = level;
          lastMessage = message;
        },
        onForceLogout: () {},
      );

      // Simulate moderately risky behavior
      for (int i = 0; i < 25; i++) {
        GyroTracker().consumeMagnitudes().add(5.0); // Moderate movement
        HoldAngleTracker().consumeHoldAngles().add(45.0); // Moderate angle
      }

      // Wait for monitoring cycle
      await Future.delayed(const Duration(seconds: 35));

      expect(lastRiskLevel, RiskLevel.moderate);
      expect(lastMessage, contains('Verification required'));
    });
  });
}
