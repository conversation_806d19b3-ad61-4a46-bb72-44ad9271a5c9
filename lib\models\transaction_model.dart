import 'package:flutter/material.dart';

enum TransactionType { credit, debit }
enum TransactionCategory { 
  salary, 
  transfer, 
  payment, 
  shopping, 
  investment, 
  interest, 
  withdrawal, 
  deposit,
  utility,
  food,
  transport,
  entertainment,
  other 
}

class Transaction {
  final String id;
  final String title;
  final String description;
  final double amount;
  final TransactionType type;
  final TransactionCategory category;
  final DateTime date;
  final IconData icon;
  final String? recipientName;
  final String? recipientAccount;
  final String? referenceNumber;
  final Map<String, dynamic>? metadata;
  final bool isVerified;

  const Transaction({
    required this.id,
    required this.title,
    required this.description,
    required this.amount,
    required this.type,
    required this.category,
    required this.date,
    required this.icon,
    this.recipientName,
    this.recipientAccount,
    this.referenceNumber,
    this.metadata,
    this.isVerified = false,
  });

  bool get isCredit => type == TransactionType.credit;
  bool get isDebit => type == TransactionType.debit;

  String get formattedAmount {
    final sign = isCredit ? '+' : '-';
    return '$sign₹${amount.toStringAsFixed(2)}';
  }

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'amount': amount,
      'type': type.toString(),
      'category': category.toString(),
      'date': date.toIso8601String(),
      'icon': icon.codePoint,
      'recipientName': recipientName,
      'recipientAccount': recipientAccount,
      'referenceNumber': referenceNumber,
      'metadata': metadata,
      'isVerified': isVerified,
    };
  }

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      amount: (json['amount'] as num).toDouble(),
      type: TransactionType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
      category: TransactionCategory.values.firstWhere(
        (e) => e.toString() == json['category'],
      ),
      date: DateTime.parse(json['date'] as String),
      icon: json['icon'] != null ? 
           IconData(json['icon'] as int) : 
           getIconForCategory(TransactionCategory.values.firstWhere(
             (e) => e.toString() == json['category'],
           )),
      recipientName: json['recipientName'] as String?,
      recipientAccount: json['recipientAccount'] as String?,
      referenceNumber: json['referenceNumber'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      isVerified: json['isVerified'] as bool? ?? false,
    );
  }

  static IconData getIconForCategory(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.salary:
        return Icons.account_balance;
      case TransactionCategory.transfer:
        return Icons.person;
      case TransactionCategory.payment:
        return Icons.payment;
      case TransactionCategory.shopping:
        return Icons.shopping_cart;
      case TransactionCategory.investment:
        return Icons.trending_up;
      case TransactionCategory.interest:
        return Icons.trending_up;
      case TransactionCategory.withdrawal:
        return Icons.atm;
      case TransactionCategory.deposit:
        return Icons.account_balance;
      case TransactionCategory.utility:
        return Icons.electrical_services;
      case TransactionCategory.food:
        return Icons.restaurant;
      case TransactionCategory.transport:
        return Icons.directions_car;
      case TransactionCategory.entertainment:
        return Icons.movie;
      case TransactionCategory.other:
        return Icons.more_horiz;
    }
  }
}

class TransactionSummary {
  final double totalIncome;
  final double totalExpense;
  final double netAmount;
  final int transactionCount;
  final DateTime periodStart;
  final DateTime periodEnd;

  TransactionSummary({
    required this.totalIncome,
    required this.totalExpense,
    required this.netAmount,
    required this.transactionCount,
    required this.periodStart,
    required this.periodEnd,
  });

  String get formattedIncome => '₹${totalIncome.toStringAsFixed(2)}';
  String get formattedExpense => '₹${totalExpense.toStringAsFixed(2)}';
  String get formattedNet => '₹${netAmount.toStringAsFixed(2)}';
}
