import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BiometricService {
  static final BiometricService _instance = BiometricService._internal();
  factory BiometricService() => _instance;
  BiometricService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _lastBiometricAuthKey = 'last_biometric_auth';

  /// Check if biometric authentication is available on the device
  Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.isDeviceSupported();
      final bool canCheckBiometrics = await _localAuth.canCheckBiometrics;
      return isAvailable && canCheckBiometrics;
    } catch (e) {
      print('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      print('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Check if user has enrolled biometrics
  Future<bool> hasEnrolledBiometrics() async {
    try {
      final availableBiometrics = await getAvailableBiometrics();
      return availableBiometrics.isNotEmpty;
    } catch (e) {
      print('Error checking enrolled biometrics: $e');
      return false;
    }
  }

  /// Authenticate using biometrics
  Future<BiometricAuthResult> authenticateWithBiometrics({
    String reason = 'Please authenticate to access your banking account',
  }) async {
    try {
      // Check if biometrics are available
      if (!await isBiometricAvailable()) {
        return BiometricAuthResult(
          success: false,
          errorMessage: 'Biometric authentication is not available on this device',
          errorType: BiometricErrorType.notAvailable,
        );
      }

      // Check if user has enrolled biometrics
      if (!await hasEnrolledBiometrics()) {
        return BiometricAuthResult(
          success: false,
          errorMessage: 'No biometrics enrolled. Please set up fingerprint or face recognition in device settings',
          errorType: BiometricErrorType.notEnrolled,
        );
      }

      // Perform authentication
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: reason,
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (didAuthenticate) {
        await _recordSuccessfulAuth();
        return BiometricAuthResult(
          success: true,
          errorMessage: null,
          errorType: null,
        );
      } else {
        return BiometricAuthResult(
          success: false,
          errorMessage: 'Authentication was cancelled or failed',
          errorType: BiometricErrorType.userCancel,
        );
      }
    } on PlatformException catch (e) {
      String errorMessage;
      BiometricErrorType errorType;

      switch (e.code) {
        case auth_error.notAvailable:
          errorMessage = 'Biometric authentication is not available';
          errorType = BiometricErrorType.notAvailable;
          break;
        case auth_error.notEnrolled:
          errorMessage = 'No biometrics enrolled on this device';
          errorType = BiometricErrorType.notEnrolled;
          break;
        case auth_error.lockedOut:
          errorMessage = 'Biometric authentication is temporarily locked. Please try again later';
          errorType = BiometricErrorType.lockedOut;
          break;
        case auth_error.permanentlyLockedOut:
          errorMessage = 'Biometric authentication is permanently locked. Please use password';
          errorType = BiometricErrorType.permanentlyLockedOut;
          break;
        default:
          errorMessage = 'Biometric authentication failed: ${e.message}';
          errorType = BiometricErrorType.unknown;
      }

      return BiometricAuthResult(
        success: false,
        errorMessage: errorMessage,
        errorType: errorType,
      );
    } catch (e) {
      return BiometricAuthResult(
        success: false,
        errorMessage: 'Unexpected error during biometric authentication: $e',
        errorType: BiometricErrorType.unknown,
      );
    }
  }

  /// Check if biometric authentication is enabled for the app
  Future<bool> isBiometricEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_biometricEnabledKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Enable/disable biometric authentication for the app
  Future<void> setBiometricEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_biometricEnabledKey, enabled);
    } catch (e) {
      print('Error setting biometric enabled state: $e');
    }
  }

  /// Record successful biometric authentication
  Future<void> _recordSuccessfulAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastBiometricAuthKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      print('Error recording biometric auth: $e');
    }
  }

  /// Get the time of last successful biometric authentication
  Future<DateTime?> getLastBiometricAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_lastBiometricAuthKey);
      return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
    } catch (e) {
      return null;
    }
  }

  /// Get user-friendly biometric type name
  String getBiometricTypeName(List<BiometricType> types) {
    if (types.contains(BiometricType.face)) {
      return 'Face Recognition';
    } else if (types.contains(BiometricType.fingerprint)) {
      return 'Fingerprint';
    } else if (types.contains(BiometricType.iris)) {
      return 'Iris Recognition';
    } else {
      return 'Biometric Authentication';
    }
  }

  /// Get appropriate icon for biometric type
  String getBiometricIcon(List<BiometricType> types) {
    if (types.contains(BiometricType.face)) {
      return 'face_recognition';
    } else if (types.contains(BiometricType.fingerprint)) {
      return 'fingerprint';
    } else {
      return 'security';
    }
  }
}

class BiometricAuthResult {
  final bool success;
  final String? errorMessage;
  final BiometricErrorType? errorType;

  BiometricAuthResult({
    required this.success,
    this.errorMessage,
    this.errorType,
  });
}

enum BiometricErrorType {
  notAvailable,
  notEnrolled,
  userCancel,
  lockedOut,
  permanentlyLockedOut,
  unknown,
}
