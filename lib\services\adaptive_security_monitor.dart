import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/behavioral_data_model.dart';
import '../models/risk_level.dart';
import 'enhanced_security_service.dart';
import 'local_auth_service.dart';
import 'competitive_ml_engine.dart';
import 'masumi_integration_service_clean_fixed.dart';
import 'unique_usp_engine.dart';
import 'package:local_auth/local_auth.dart';

enum SecurityAction {
  none,
  requireBiometric,
  forceLogout
}

class AdaptiveSecurityMonitor {
  static final AdaptiveSecurityMonitor _instance = AdaptiveSecurityMonitor._internal();
  static AdaptiveSecurityMonitor get instance => _instance;
  factory AdaptiveSecurityMonitor() => _instance;
  AdaptiveSecurityMonitor._internal();

  Function(RiskLevel, String)? _onRiskLevelChanged;
  Function()? _onForceLogout;

  // Get monitoring statistics
  Map<String, dynamic> getMonitoringStats() {
    return {
      'is_monitoring': _isMonitoring,
      'current_risk_score': _lastRiskScore ?? 0.0,
      'anomaly_stats': _getAnomalyStats(),
    };
  }

  double? _lastRiskScore;

  Map<String, dynamic> _getAnomalyStats() {
    return {
      'total_checks': _totalChecks,
      'anomalies_detected': _anomaliesDetected,
      'last_check_timestamp': _lastCheckTimestamp,
    };
  }

  int _totalChecks = 0;
  int _anomaliesDetected = 0;
  int? _lastCheckTimestamp;

  // Core services
  final CompetitiveMLEngine _mlEngine = CompetitiveMLEngine.instance;
  final UniqueUSPEngine _uspEngine = UniqueUSPEngine.instance;
  final MasumiIntegrationService _masumiService = MasumiIntegrationService();
  final LocalAuthService _localAuthService = LocalAuthService();
  
  // Risk thresholds (lowered for demo sensitivity)
  static const double _highRiskThreshold = 0.6;
  static const double _moderateRiskThreshold = 0.4;

  // Monitoring state
  bool _isMonitoring = false;
  Timer? _monitoringTimer;
  StreamController<SecurityAction>? _actionController;
  
  // Initialize the monitor
  Future<void> startMonitoring({
    Function(RiskLevel, String)? onRiskLevelChanged,
    Function()? onForceLogout,
  }) async {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    _actionController = StreamController<SecurityAction>.broadcast();
    _onRiskLevelChanged = onRiskLevelChanged;
    _onForceLogout = onForceLogout;
    
    // Start periodic monitoring (faster for demo)
    _monitoringTimer = Timer.periodic(
      const Duration(seconds: 3),
      (_) => _performSecurityCheck(),
    );
    
    debugPrint('🔒 Adaptive security monitoring started');
  }
  
  // Stop monitoring
  void stopMonitoring() {
    _monitoringTimer?.cancel();
    _actionController?.close();
    _isMonitoring = false;
    debugPrint('🔒 Adaptive security monitoring stopped');
  }
  
  // Get security action stream
  Stream<SecurityAction> get securityActions => 
      _actionController?.stream ?? const Stream.empty();
  
  // Main security check method
  Future<void> _performSecurityCheck() async {
    try {
      final behavioralData = await _collectBehavioralData();
      final riskScore = await _calculateRiskScore(behavioralData);
      
      debugPrint('🔍 Current risk score: ${riskScore.toStringAsFixed(2)}');
      
      // Determine action based on risk score
      SecurityAction action = _determineSecurityAction(riskScore);
      
      if (action != SecurityAction.none) {
        _actionController?.add(action);
        _logSecurityEvent(riskScore, action);
      }
      
    } catch (e) {
      debugPrint('❌ Error in security check: $e');
    }
  }
  
  // Collect current behavioral data
  Future<Map<String, dynamic>> _collectBehavioralData() async {
    // Get latest behavioral data from various sources
    final mlFeatures = await _mlEngine.extractCurrentFeatures();
    final privacyMetrics = _masumiService._extractMotionFeatures({'current': true});
    
    return {
      ...mlFeatures,
      ...privacyMetrics,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }
  
  // Calculate comprehensive risk score
  Future<double> _calculateRiskScore(Map<String, dynamic> behavioralData) async {
    double riskScore = 0.0;

    // Enhanced risk calculation for demo
    debugPrint('🔍 Calculating risk score from behavioral data...');

    // Check for fast swipe patterns
    if (behavioralData.containsKey('swipe_velocities')) {
      final swipeVelocities = behavioralData['swipe_velocities'] as List<double>? ?? [];
      if (swipeVelocities.isNotEmpty) {
        final maxVelocity = swipeVelocities.reduce((a, b) => a > b ? a : b);
        final avgVelocity = swipeVelocities.reduce((a, b) => a + b) / swipeVelocities.length;

        debugPrint('📱 Swipe Analysis: Max=${maxVelocity.toStringAsFixed(1)}, Avg=${avgVelocity.toStringAsFixed(1)}');

        // High velocity swipes increase risk significantly
        if (maxVelocity > 1500) {
          riskScore += 0.5;
          debugPrint('⚡ VERY FAST SWIPE DETECTED: +0.5 risk');
        } else if (maxVelocity > 1000) {
          riskScore += 0.3;
          debugPrint('⚡ FAST SWIPE DETECTED: +0.3 risk');
        }

        // Consistent high velocity
        if (avgVelocity > 800) {
          riskScore += 0.2;
          debugPrint('📈 HIGH AVERAGE VELOCITY: +0.2 risk');
        }
      }
    }

    // Check for unusual gyro/angle data
    if (behavioralData.containsKey('gyro_data')) {
      final gyroData = behavioralData['gyro_data'] as Map<String, dynamic>? ?? {};
      if (gyroData.isNotEmpty) {
        final maxAngle = gyroData['max_angle'] as double? ?? 0.0;
        final avgAngle = gyroData['avg_angle'] as double? ?? 0.0;

        debugPrint('📐 Gyro Analysis: Max=${maxAngle.toStringAsFixed(1)}°, Avg=${avgAngle.toStringAsFixed(1)}°');

        if (maxAngle > 45) {
          riskScore += 0.3;
          debugPrint('🔄 HIGH ANGLE MOVEMENT: +0.3 risk');
        } else if (maxAngle > 30) {
          riskScore += 0.1;
          debugPrint('🔄 MODERATE ANGLE MOVEMENT: +0.1 risk');
        }
      }
    }

    // Get scores from ML engines
    try {
      final mlScore = await _mlEngine.authenticateUser(behavioralData);
      final uspScore = await _uspEngine._analyzeFraudPatterns(behavioralData);

      // Add ML-based risk
      final mlRisk = (mlScore['risk_level'] == 'high' ? 0.4 :
                     mlScore['risk_level'] == 'medium' ? 0.2 : 0.1);
      riskScore += mlRisk;
      debugPrint('🤖 ML Risk Assessment: +${mlRisk.toStringAsFixed(1)} risk');

      // Add USP-based risk
      riskScore += uspScore * 0.2;
      debugPrint('🔒 USP Risk Assessment: +${(uspScore * 0.2).toStringAsFixed(1)} risk');

    } catch (e) {
      debugPrint('❌ Error in ML risk calculation: $e');
    }

    final finalRisk = riskScore.clamp(0.0, 1.0);
    debugPrint('📊 FINAL RISK SCORE: ${finalRisk.toStringAsFixed(3)}');

    return finalRisk;
  }
  
  // Determine appropriate security action
  SecurityAction _determineSecurityAction(double riskScore) {
    if (riskScore >= _highRiskThreshold) {
      return SecurityAction.forceLogout;
    } else if (riskScore >= _moderateRiskThreshold) {
      return SecurityAction.requireBiometric;
    }
    return SecurityAction.none;
  }
  
  // Log security events
  void _logSecurityEvent(double riskScore, SecurityAction action) {
    final timestamp = DateTime.now().toIso8601String();
    final actionStr = action.toString().split('.').last;

    debugPrint('═══════════════════════════════════════════════════════');
    debugPrint('🚨 SECURITY EVENT TRIGGERED');
    debugPrint('═══════════════════════════════════════════════════════');
    debugPrint('⏰ Timestamp: $timestamp');
    debugPrint('📊 Risk Score: ${riskScore.toStringAsFixed(3)}');
    debugPrint('🎯 Action Taken: $actionStr');

    if (action == SecurityAction.forceLogout) {
      debugPrint('🔴 CRITICAL: AUTO-LOGOUT INITIATED');
      debugPrint('🔴 Reason: Risk score exceeded threshold (${_highRiskThreshold})');
    } else if (action == SecurityAction.requireBiometric) {
      debugPrint('🟡 WARNING: BIOMETRIC VERIFICATION REQUIRED');
      debugPrint('🟡 Reason: Risk score exceeded threshold (${_moderateRiskThreshold})');
    }

    debugPrint('═══════════════════════════════════════════════════════');
  }
}
