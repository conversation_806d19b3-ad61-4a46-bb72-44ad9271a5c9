import 'dart:math' as math;

import '../models/behavioral_data_model.dart';

/// Advanced feature extraction for behavioral biometric authentication
class FeatureExtractor {
  /// Extract comprehensive feature vector from behavioral data
  List<double> extractFeatures(List<BehavioralData> behavioralSequence) {
    if (behavioralSequence.isEmpty) {
      return List.filled(getFeatureCount(), 0.0);
    }
    
    final features = <double>[];
    
    // 1. Basic statistical features (5 features)
    features.addAll(_extractBasicFeatures(behavioralSequence.last));
    
    // 2. Temporal pattern features (8 features)
    features.addAll(_extractTemporalFeatures(behavioralSequence));
    
    // 3. Velocity and acceleration features (6 features)
    features.addAll(_extractMotionFeatures(behavioralSequence));
    
    // 4. Pressure dynamics features (4 features)
    features.addAll(_extractPressureFeatures(behavioralSequence));
    
    // 5. Rhythm and timing features (5 features)
    features.addAll(_extractRhythmFeatures(behavioralSequence));
    
    // 6. Device orientation features (3 features)
    features.addAll(_extractOrientationFeatures(behavioralSequence));
    
    // 7. Consistency and stability features (4 features)
    features.addAll(_extractConsistencyFeatures(behavioralSequence));
    
    return features;
  }
  
  /// Extract basic statistical features from single behavioral sample
  List<double> _extractBasicFeatures(BehavioralData data) {
    return [
      data.touchPressures.isNotEmpty ? data.touchPressures.last : 0.0,
      data.dwellTimes.isNotEmpty ? data.dwellTimes.last.toDouble() : 0.0,
      data.swipeVelocities.isNotEmpty ? data.swipeVelocities.last : 0.0,
      data.gyroMagnitudes.isNotEmpty ? data.gyroMagnitudes.last : 0.0,
      data.keystrokeIntervals.isNotEmpty ? data.keystrokeIntervals.last.toDouble() : 0.0,
    ];
  }
  
  /// Extract temporal pattern features from sequence
  List<double> _extractTemporalFeatures(List<BehavioralData> sequence) {
    if (sequence.length < 2) {
      return List.filled(8, 0.0);
    }
    
    final features = <double>[];
    
    // Time intervals between actions
    final intervals = <double>[];
    for (int i = 1; i < sequence.length; i++) {
      // Use session start time as a proxy for timestamp
      final interval = (sequence[i].sessionStartTime - sequence[i-1].sessionStartTime).toDouble();
      intervals.add(interval.abs());
    }
    
    if (intervals.isNotEmpty) {
      features.add(_calculateMean(intervals));
      features.add(_calculateStandardDeviation(intervals));
      features.add(intervals.reduce(math.min));
      features.add(intervals.reduce(math.max));
    } else {
      features.addAll([0.0, 0.0, 0.0, 0.0]);
    }
    
    // Sequence length and density
    features.add(sequence.length.toDouble());
    
    // Time span of sequence
    final timeSpan = (sequence.last.sessionStartTime - sequence.first.sessionStartTime).toDouble().abs();
    features.add(timeSpan);
    
    // Action density (actions per second)
    features.add(timeSpan > 0 ? (sequence.length / (timeSpan / 1000.0)) : 0.0);
    
    // Temporal regularity (coefficient of variation)
    features.add(intervals.isNotEmpty && _calculateMean(intervals) > 0 
        ? _calculateStandardDeviation(intervals) / _calculateMean(intervals) 
        : 0.0);
    
    return features;
  }
  
  /// Extract motion-based features (velocity, acceleration)
  List<double> _extractMotionFeatures(List<BehavioralData> sequence) {
    if (sequence.length < 2) {
      return List.filled(6, 0.0);
    }
    
    final velocities = sequence.expand((d) => d.swipeVelocities).toList();
    final accelerations = <double>[];
    
    // Calculate accelerations
    for (int i = 1; i < velocities.length; i++) {
      accelerations.add(velocities[i] - velocities[i-1]);
    }
    
    return [
      _calculateMean(velocities),
      _calculateStandardDeviation(velocities),
      velocities.reduce(math.max),
      accelerations.isNotEmpty ? _calculateMean(accelerations) : 0.0,
      accelerations.isNotEmpty ? _calculateStandardDeviation(accelerations) : 0.0,
      accelerations.isNotEmpty ? accelerations.reduce(math.max) : 0.0,
    ];
  }
  
  /// Extract pressure dynamics features
  List<double> _extractPressureFeatures(List<BehavioralData> sequence) {
    final pressures = sequence.expand((d) => d.touchPressures).toList();

    if (pressures.isEmpty) {
      return [0.0, 0.0, 0.0, 0.0];
    }

    return [
      _calculateMean(pressures),
      _calculateStandardDeviation(pressures),
      pressures.reduce(math.min),
      pressures.reduce(math.max),
    ];
  }
  
  /// Extract rhythm and timing features
  List<double> _extractRhythmFeatures(List<BehavioralData> sequence) {
    final rhythms = sequence.expand((d) => d.keystrokeIntervals.map((i) => i.toDouble())).toList();
    final durations = sequence.expand((d) => d.dwellTimes.map((i) => i.toDouble())).toList();

    return [
      rhythms.isNotEmpty ? _calculateMean(rhythms) : 0.0,
      rhythms.isNotEmpty ? _calculateStandardDeviation(rhythms) : 0.0,
      durations.isNotEmpty ? _calculateMean(durations) : 0.0,
      durations.isNotEmpty ? _calculateStandardDeviation(durations) : 0.0,
      durations.isNotEmpty && _calculateMean(durations) > 0
          ? _calculateStandardDeviation(durations) / _calculateMean(durations)
          : 0.0,
    ];
  }
  
  /// Extract device orientation features
  List<double> _extractOrientationFeatures(List<BehavioralData> sequence) {
    final tilts = sequence.expand((d) => d.gyroMagnitudes).toList();

    if (tilts.isEmpty) {
      return [0.0, 0.0, 0.0];
    }

    return [
      _calculateMean(tilts),
      _calculateStandardDeviation(tilts),
      tilts.reduce(math.max) - tilts.reduce(math.min),
    ];
  }
  
  /// Extract consistency and stability features
  List<double> _extractConsistencyFeatures(List<BehavioralData> sequence) {
    if (sequence.length < 3) {
      return List.filled(4, 0.0);
    }
    
    // Calculate feature stability across sequence
    final pressureStability = _calculateStability(sequence.expand((d) => d.touchPressures).toList());
    final velocityStability = _calculateStability(sequence.expand((d) => d.swipeVelocities).toList());
    final rhythmStability = _calculateStability(sequence.expand((d) => d.keystrokeIntervals.map((i) => i.toDouble())).toList());
    
    // Overall behavioral consistency score
    final consistencyScore = (pressureStability + velocityStability + rhythmStability) / 3.0;
    
    return [
      pressureStability,
      velocityStability,
      rhythmStability,
      consistencyScore,
    ];
  }
  
  /// Calculate stability metric for a feature sequence
  double _calculateStability(List<double> values) {
    if (values.length < 2) return 1.0;
    
    final mean = _calculateMean(values);
    final std = _calculateStandardDeviation(values);
    
    // Stability = 1 / (1 + coefficient_of_variation)
    return mean > 0 ? 1.0 / (1.0 + (std / mean)) : 1.0;
  }
  
  /// Calculate mean of a list
  double _calculateMean(List<double> values) {
    if (values.isEmpty) return 0.0;
    return values.reduce((a, b) => a + b) / values.length;
  }
  
  /// Calculate standard deviation of a list
  double _calculateStandardDeviation(List<double> values) {
    if (values.length < 2) return 0.0;
    
    final mean = _calculateMean(values);
    final variance = values.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) / values.length;
    return math.sqrt(variance);
  }
  
  /// Get total number of features extracted
  int getFeatureCount() {
    return 35; // 5 + 8 + 6 + 4 + 5 + 3 + 4 = 35 features
  }
  
  /// Get feature names for interpretability
  List<String> getFeatureNames() {
    return [
      // Basic features (5)
      'touch_pressure', 'touch_duration', 'swipe_velocity', 'device_tilt', 'typing_rhythm',
      
      // Temporal features (8)
      'mean_interval', 'interval_variability', 'min_interval', 'max_interval',
      'sequence_length', 'time_span', 'action_density', 'temporal_regularity',
      
      // Motion features (6)
      'mean_velocity', 'velocity_variability', 'peak_velocity',
      'mean_acceleration', 'acceleration_variability', 'peak_acceleration',
      
      // Pressure features (4)
      'mean_pressure', 'pressure_variability', 'min_pressure', 'max_pressure',
      
      // Rhythm features (5)
      'mean_rhythm', 'rhythm_variability', 'mean_duration', 'duration_variability', 'duration_consistency',
      
      // Orientation features (3)
      'mean_tilt', 'tilt_variability', 'tilt_range',
      
      // Consistency features (4)
      'pressure_stability', 'velocity_stability', 'rhythm_stability', 'overall_consistency',
    ];
  }
  
  /// Normalize features using z-score normalization
  List<double> normalizeFeatures(List<double> features, List<double> means, List<double> stds) {
    final normalized = <double>[];
    
    for (int i = 0; i < features.length; i++) {
      if (i < means.length && i < stds.length && stds[i] > 0) {
        normalized.add((features[i] - means[i]) / stds[i]);
      } else {
        normalized.add(features[i]);
      }
    }
    
    return normalized;
  }
  
  /// Calculate feature importance based on variance
  Map<String, double> calculateFeatureImportance(List<List<double>> featureMatrix) {
    final importance = <String, double>{};
    final featureNames = getFeatureNames();
    
    if (featureMatrix.isEmpty) return importance;
    
    final featureCount = featureMatrix.first.length;
    
    for (int i = 0; i < featureCount && i < featureNames.length; i++) {
      final featureValues = featureMatrix.map((row) => i < row.length ? row[i] : 0.0).toList();
      final variance = _calculateStandardDeviation(featureValues);
      importance[featureNames[i]] = variance;
    }
    
    return importance;
  }
}
