import 'dart:math' as math;
import '../ml/ocsvm_engine.dart';
import '../models/behavioral_data_model.dart';

/// OCSVM Service Adapter - Bridges between existing behavioral data and OCSVM engine
/// Provides seamless integration with the current Trust Chain authentication system
class OCSVMService {
  static final OCSVMService _instance = OCSVMService._internal();
  factory OCSVMService() => _instance;
  OCSVMService._internal();

  final OCSVMEngine _ocsvm = OCSVMEngine();
  
  // Training data buffer
  final List<BehavioralData> _trainingBuffer = [];
  static const int _minTrainingSize = 15; // Minimum samples for reliable training
  static const int _maxTrainingBuffer = 100; // Maximum buffer size
  
  bool _isTraining = false;
  bool get isReady => _ocsvm.isReady;
  bool get isTraining => _isTraining;

  /// Trigger a health check of the OCSVM model
  Future<Map<String, dynamic>> triggerHealthCheck() async {
    if (_ocsvm.isReady) {
      final performanceMetrics = await _ocsvm.evaluateModelHealth();
      final accuracy = performanceMetrics['accuracy'] ?? 0.0;
      final falsePositives = performanceMetrics['falsePositives'] ?? 0.0;
      
      print('[OCSVM Service] Health Check Results:');
      print('  - Model Accuracy: ${(accuracy * 100).toStringAsFixed(1)}%');
      print('  - False Positive Rate: ${(falsePositives * 100).toStringAsFixed(1)}%');

      return performanceMetrics;
    } else {
      print('[OCSVM Service] Model not ready for health check');
      return {
        'status': 'not_ready',
        'accuracy': 0.0,
        'falsePositives': 0.0
      };
    }
  }

  Future<List<double>> _getRiskScores() async {
    final scores = <double>[];
    // Get last 10 risk scores or available amount
    return scores;
  }

  Future<void> detectAnomalies(Map<String, List<double>> data) async {
    // Convert raw data to behavioral data format
    final behavioralData = BehavioralData.fromRawData(data);
    
    // Use OCSVM to detect anomalies
    if (_ocsvm.isReady) {
      final anomalyScore = await _ocsvm.predict(behavioralData);
      print('[OCSVM Service] Anomaly Score: $anomalyScore');
    }
  }

  /// Process behavioral data for anomaly detection
  Future<void> processBehavioralData(Map<String, List<double>> data) async {
    if (!_ocsvm.isReady) {
      print('[OCSVM Service] Model not ready. Data will be used for training.');
      final behavioralData = BehavioralData.fromRawData(data);
      await addTrainingData(behavioralData);
    } else {
      await detectAnomalies(data);
    }
  }

  /// Evaluate current risk level based on recent behavioral patterns
  Future<double> evaluateCurrentRisk() async {
    if (!_ocsvm.isReady) {
      return 0.0;  // Default low risk when model isn't ready
    }

    // Get recent behavioral data
    final recentScores = await _getRiskScores();
    if (recentScores.isEmpty) return 0.0;

    // Calculate weighted average of recent scores
    double weightedSum = 0;
    double weightSum = 0;
    for (int i = 0; i < recentScores.length; i++) {
      double weight = (i + 1) / recentScores.length;  // More recent = higher weight
      weightedSum += recentScores[i] * weight;
      weightSum += weight;
    }

    return weightedSum / weightSum;
  }

  /// Add behavioral data for training
  Future<void> addTrainingData(BehavioralData behavioralData) async {
    print('[OCSVM Service] Adding training data sample');
    
    _trainingBuffer.add(behavioralData);
    
    // Keep buffer size manageable
    if (_trainingBuffer.length > _maxTrainingBuffer) {
      _trainingBuffer.removeAt(0); // Remove oldest sample
    }
    
    // Auto-train when we have enough samples
    if (_trainingBuffer.length >= _minTrainingSize && !_isTraining) {
      print('[OCSVM Service] Sufficient training data available, starting auto-training');
      await trainModel();
    }
  }

  /// Train the OCSVM model
  Future<void> trainModel() async {
    if (_trainingBuffer.length < _minTrainingSize) {
      print('[OCSVM Service] Insufficient training data: ${_trainingBuffer.length}/$_minTrainingSize');
      return;
    }

    if (_isTraining) {
      print('[OCSVM Service] Training already in progress');
      return;
    }

    _isTraining = true;
    
    try {
      print('[OCSVM Service] Converting ${_trainingBuffer.length} samples to OCSVM format');
      
      // Convert current behavioral data to OCSVM format
      final ocsvmData = _trainingBuffer.map((data) => _convertToOCSVMFormat(data)).toList();
      
      // Train the OCSVM model
      await _ocsvm.trainLocal(ocsvmData);
      
      print('[OCSVM Service] ✅ Model training completed successfully');
      
    } catch (e) {
      print('[OCSVM Service] ❌ Training failed: $e');
    } finally {
      _isTraining = false;
    }
  }

  /// Authenticate user using OCSVM
  Future<OCSVMAuthResult> authenticate(BehavioralData behavioralData) async {
    if (!_ocsvm.isReady) {
      // If not trained yet, add to training buffer and return default result
      await addTrainingData(behavioralData);
      return OCSVMAuthResult(
        isAuthenticated: true, // Allow access during training phase
        score: 0.5,
        confidence: 0.5,
        riskLevel: 'TRAINING',
        message: 'Model training in progress'
      );
    }

    try {
      // Convert to OCSVM format
      final ocsvmData = _convertToOCSVMFormat(behavioralData);
      
      // Get authentication result
      final result = await _ocsvm.authenticateEnhanced([ocsvmData]);
      
      // Convert to our result format
      return OCSVMAuthResult(
        isAuthenticated: result.isAuthenticated,
        score: result.score,
        confidence: result.confidence,
        riskLevel: result.riskLevel,
        message: 'OCSVM authentication completed'
      );
      
    } catch (e) {
      print('[OCSVM Service] Authentication error: $e');
      return OCSVMAuthResult(
        isAuthenticated: false,
        score: -1.0,
        confidence: 0.0,
        riskLevel: 'ERROR',
        message: 'Authentication failed: $e'
      );
    }
  }

  /// Convert Trust Chain BehavioralData to OCSVM BehavioralData format
  BehavioralData _convertToOCSVMFormat(BehavioralData data) {
    // Calculate aggregated metrics from Trust Chain behavioral data
    
    // Touch pressure from tap positions and available pressure data
    double touchPressure = 0.5; // Default
    if (data.touchPressures.isNotEmpty) {
      touchPressure = data.touchPressures.reduce((a, b) => a + b) / data.touchPressures.length;
    }

    // Touch duration from dwell times and keystroke intervals
    double touchDuration = 200.0; // Default in milliseconds
    if (data.dwellTimes.isNotEmpty) {
      touchDuration = data.dwellTimes.reduce((a, b) => a + b) / data.dwellTimes.length;
    } else if (data.keystrokeIntervals.isNotEmpty) {
      touchDuration = data.keystrokeIntervals.reduce((a, b) => a + b) / data.keystrokeIntervals.length;
    }

    // Swipe velocity from swipe data
    double swipeVelocity = 0.0;
    if (data.swipeVelocities.isNotEmpty) {
      swipeVelocity = data.swipeVelocities.reduce((a, b) => a + b) / data.swipeVelocities.length;
    }

    // Device tilt from hold angles
    double deviceTilt = 0.0;
    if (data.holdAngles != null && data.holdAngles!.isNotEmpty) {
      deviceTilt = data.holdAngles!.reduce((a, b) => a + b) / data.holdAngles!.length;
    }

    // Typing rhythm from keystroke intervals
    double typingRhythm = 0.0;
    if (data.keystrokeIntervals.isNotEmpty) {
      // Calculate coefficient of variation as rhythm metric
      final mean = data.keystrokeIntervals.reduce((a, b) => a + b) / data.keystrokeIntervals.length;
      final variance = data.keystrokeIntervals
          .map((interval) => math.pow(interval - mean, 2))
          .reduce((a, b) => a + b) / data.keystrokeIntervals.length;
      final stdDev = math.sqrt(variance);
      typingRhythm = mean > 0 ? stdDev / mean : 0.0;
    }

    return BehavioralData(
      touchPressures: [touchPressure],
      swipeVelocities: [swipeVelocity],
      gyroMagnitudes: [deviceTilt],
      keystrokeIntervals: [typingRhythm.toInt()],
    );
  }

  /// Get OCSVM model statistics
  Map<String, dynamic> getModelStats() {
    final stats = _ocsvm.getStats();
    stats['training_buffer_size'] = _trainingBuffer.length;
    stats['min_training_size'] = _minTrainingSize;
    stats['is_training'] = _isTraining;
    return stats;
  }

  /// Force retrain the model (useful for testing)
  Future<void> forceRetrain() async {
    print('[OCSVM Service] Force retraining requested');
    _isTraining = false; // Reset training flag
    await trainModel();
  }

  /// Clear training buffer and reset model
  void resetModel() {
    print('[OCSVM Service] Resetting model and clearing training buffer');
    _trainingBuffer.clear();
    _isTraining = false;
  }

  /// Get privacy status
  String get privacyStatus => _ocsvm.privacyStatus;
}

/// OCSVM Authentication Result
class OCSVMAuthResult {
  final bool isAuthenticated;
  final double score;
  final double confidence;
  final String riskLevel;
  final String message;

  OCSVMAuthResult({
    required this.isAuthenticated,
    required this.score,
    required this.confidence,
    required this.riskLevel,
    required this.message,
  });

  Map<String, dynamic> toJson() {
    return {
      'isAuthenticated': isAuthenticated,
      'score': score,
      'confidence': confidence,
      'riskLevel': riskLevel,
      'message': message,
    };
  }

  @override
  String toString() {
    return 'OCSVMAuthResult(authenticated: $isAuthenticated, score: ${score.toStringAsFixed(3)}, confidence: ${(confidence * 100).toStringAsFixed(1)}%, risk: $riskLevel)';
  }
}
