import 'dart:async';

import 'package:flutter/material.dart';
import 'package:sensors_plus/sensors_plus.dart';
import 'package:trust_chain_banking/firebase/firebase_service.dart';
import 'package:trust_chain_banking/models/behavioral_data_model.dart';
import 'package:trust_chain_banking/models/risk_level.dart';
import 'package:trust_chain_banking/services/auth_service.dart';
import 'package:trust_chain_banking/services/gyro_tracker.dart';
import 'package:trust_chain_banking/services/hold_angle_tracker.dart';
import 'package:trust_chain_banking/services/ocsvm_service.dart';
import '../utils/constants.dart' as constants;
import '../utils/currency_formatter.dart';
import '../routes/app_routes.dart';
import '../services/transaction_service.dart';
import '../models/transaction_model.dart';
import '../services/adaptive_security_monitor.dart';
import '../services/local_auth_service.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> with WidgetsBindingObserver {
  final TransactionService _transactionService = TransactionService();
  final HoldAngleTracker _holdAngleTracker = HoldAngleTracker();
  final OCSVMService _ocsvmService = OCSVMService();
  final AdaptiveSecurityMonitor _securityMonitor = AdaptiveSecurityMonitor.instance;
  final CurrencyFormatter currencyFormatter = CurrencyFormatter();

  // State variables
  final List<double> _swipeVelocities = [];
  final Map<int, Offset> _tapPositions = {};
  double _currentBalance = 0.0;
  String _userName = 'User';
  bool _isLoading = true;
  bool _hasUploadedBehavioralData = false;
  bool _showingBiometricPrompt = false;

  // Session management
  DateTime? _sessionStartTime;
  String? _sessionId;
  Timer? _uploadTimer;
  Timer? _debugTimer;
  Timer? _riskCheckTimer;

  // Gesture tracking
  Offset? _swipeStart;
  DateTime? _swipeStartTime;

  // Transaction data
  List<Transaction> _recentTransactions = [];

  // Constants
  final String _accountNumber = constants.BankingConstants.defaultAccountNumber;
  final String _bankName = constants.BankingConstants.defaultBankName;
  Timer? _debugTimer;
  Timer? _riskCheckTimer;
  
  // Gesture tracking
  Offset? _swipeStart;
  DateTime? _swipeStartTime;

  // Transaction data
  List<Transaction> _recentTransactions = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadInitialData();
    _startSecurityMonitoring();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _debugTimer?.cancel();
    _securityMonitor.dispose();
    _holdAngleTracker.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    // Simulated initial data loading
    if (!_hasUploadedBehavioralData) {
      await _recordBehavioralData();
    }
  }
      _tapPositions.add({
        'x': details.globalPosition.dx,
        'y': details.globalPosition.dy,
      });
    });
  }

  void _showSendMoneyDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Send Money', style: TextStyle(fontSize: 20)),
            const SizedBox(height: 20),
            // Add send money form here
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }

  void _showRequestMoneyDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Request Money', style: TextStyle(fontSize: 20)),
            const SizedBox(height: 20),
            // Add request money form here
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }

  void _showTopUpDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Top Up Account', style: TextStyle(fontSize: 20)),
            const SizedBox(height: 20),
            // Add top up form here
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEmergencyOptions() {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) => AlertDialog(
        title: const Text('Emergency Options'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.lock),
              title: const Text('Lock Account'),
              onTap: () {
                // Implement lock account
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.emergency),
              title: const Text('Emergency Contact'),
              onTap: () {
                // Implement emergency contact
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Theme.of(context).primaryColor,
      title: Text(_userName),
      actions: [
        IconButton(
          icon: const Icon(Icons.security),
          onPressed: () {
            _ocsvmService.triggerHealthCheck();
          },
        ),
      ],
    );
  }

  Widget _buildBalanceSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            'Current Balance',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            currencyFormatter.format(_currentBalance),
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text('Account: $_accountNumber'),
          Text('Bank: $_bankName'),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions() {
    if (_recentTransactions.isEmpty) {
      return const Center(
        child: Text('No recent transactions'),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _recentTransactions.length,
      itemBuilder: (context, index) {
        final transaction = _recentTransactions[index];
        return ListTile(
          title: Text(transaction.description),
          subtitle: Text(transaction.date.toString()),
          trailing: Text(
            currencyFormatter.format(transaction.amount),
            style: TextStyle(
              color: transaction.amount >= 0 ? Colors.green : Colors.red,
            ),
          ),
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _sessionStartTime = DateTime.now();
    _loadInitialData();
    _setupTimers();
    _startSecurityMonitoring();
  }

  Future<void> _loadInitialData() async {
    try {
      await Future.wait([
        _loadUserData(),
        _loadDashboardData(),
      ]);
      _testSensorAvailability();
      _startDebugTimer();

      if (mounted) {
        setState(() {
          _userName = AuthService.currentUserName ?? constants.BankingConstants.defaultUserName;
        });
      }

      GyroTracker().startPassive();
      _holdAngleTracker.start();
    } catch (e) {
      debugPrint('Error in loadInitialData: $e');
    }
  }

  void _setupTimers() {
    _sessionId = DateTime.now().millisecondsSinceEpoch.toString();

    // Setup periodic data upload
    _uploadTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _uploadBehavioralData();
    });

    // Setup periodic risk checks
    _riskCheckTimer = Timer.periodic(const Duration(minutes: 15), (_) {
      _checkRiskStatus();
    });
  }

  @override
  void dispose() {
    _uploadTimer?.cancel();
    _debugTimer?.cancel();
    _riskCheckTimer?.cancel();
    _holdAngleTracker.stop();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (!mounted) return;
    
    debugPrint('[Dashboard] App lifecycle state changed to: $state');
    
    if (state == AppLifecycleState.detached || state == AppLifecycleState.inactive) {
      debugPrint('[Dashboard] App going inactive/detached - recording behavioral data');
      _recordBehavioralData();
    } else if (state == AppLifecycleState.paused) {
      debugPrint('[Dashboard] App paused - stopping tracking temporarily');
      GyroTracker().stopBurst();
    } else if (state == AppLifecycleState.resumed) {
      debugPrint('[Dashboard] App resumed - restarting tracking');
      if (!_hasUploadedBehavioralData) {
        GyroTracker().startPassive();
      }
    }
  }

  Future<void> _uploadBehavioralData() async {
    if (!_hasUploadedBehavioralData) {
      final behavioralData = _holdAngleTracker.getTrackedData();
      await _ocsvmService.processBehavioralData(behavioralData);
      _hasUploadedBehavioralData = true;
    }
  }

  Future<void> _checkRiskStatus() async {
    final riskLevel = await _ocsvmService.evaluateCurrentRisk();
    if (riskLevel > 0.7) { // High risk threshold
      _showRiskWarning();
    }
  }

  void _showRiskWarning() {
    if (mounted && context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Unusual activity detected. Please verify your identity.'),
          duration: Duration(seconds: 5),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onHorizontalDragStart: _handleSwipeStart,
      onHorizontalDragEnd: _handleSwipeEnd,
      child: Scaffold(
        body: _isLoading 
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                children: [
                  _buildAppBar(),
                  _buildBalanceSection(),
                  _buildRecentTransactions(),
                ],
              ),
            ),
      ),
    );
  }

  Future<void> _loadInitialData() async {
    try {
      await Future.wait([
        _loadUserData(),
        _loadDashboardData(),
      ]);
      _testSensorAvailability();
      _startDebugTimer();

      _userName = AuthService.currentUserName ?? constants.BankingConstants.defaultUserName;
      GyroTracker().startPassive();
      _holdAngleTracker.start();
    } catch (e) {
      debugPrint('Error in loadInitialData: $e');
    }
  }

  void _startSecurityMonitoring() {
    _securityMonitor.startMonitoring();

    _securityMonitor.securityActions.listen((action) {
      switch (action) {
        case SecurityAction.requireBiometric:
          _handleBiometricRequest();
          break;
        case SecurityAction.forceLogout:
          _handleForcedLogout();
          break;
        case SecurityAction.none:
          break;
      }
    });
  }

  Future<void> _handleBiometricRequest() async {
    if (_showingBiometricPrompt) return;
    
    _showingBiometricPrompt = true;
    try {
      final localAuth = LocalAuthService();
      final authenticated = await localAuth.authenticateWithBiometrics(
        reason: 'Verify your identity due to unusual activity',
      );

      if (!authenticated) {
        _handleForcedLogout();
      }
    } finally {
      _showingBiometricPrompt = false;
    }
  }

  void _handleForcedLogout() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Security Alert'),
        content: const Text('Unusual activity detected. For your security, you will be logged out.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushReplacementNamed(AppRoutes.login);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _testSensorAvailability() {
    debugPrint('[Dashboard] Testing sensor availability...');
    
    // Test gyroscope
    try {
      final gyroStream = gyroscopeEventStream();
      final gyroSub = gyroStream.listen((event) {
        debugPrint('[Dashboard] ✅ Gyroscope working: ${event.x}, ${event.y}, ${event.z}');
      });
      
      // Cancel after a short test
      Future.delayed(const Duration(milliseconds: 500), () {
        gyroSub.cancel();
      });
    } catch (e) {
      debugPrint('[Dashboard] ❌ Gyroscope error: $e');
    }
    
    // Test accelerometer
    try {
      final accelStream = accelerometerEventStream();
      final accelSub = accelStream.listen((event) {
        debugPrint('[Dashboard] ✅ Accelerometer working: ${event.x}, ${event.y}, ${event.z}');
      });
      
      // Cancel after a short test
      Future.delayed(const Duration(milliseconds: 500), () {
        accelSub.cancel();
      });
    } catch (e) {
      debugPrint('[Dashboard] ❌ Accelerometer error: $e');
    }
  }

  void _startDebugTimer() {
    if (_debugTimer != null) return;
    
    _debugTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      final sessionDuration = _sessionStartTime != null ? DateTime.now().difference(_sessionStartTime!).inSeconds : 0;
      
      debugPrint('[Dashboard Debug] Current behavioral data status:');
      debugPrint('  - Tap positions: ${_tapPositions.length}');
      debugPrint('  - Swipe velocities: ${_swipeVelocities.length}');
      debugPrint('  - Session duration: ${sessionDuration}s');
      
      // Check OCSVM status
      final ocsvmStats = _ocsvmService.getModelStats();
      debugPrint('[Dashboard Debug] OCSVM Status:');
      debugPrint('  - Model trained: ${ocsvmStats['is_trained']}');
      debugPrint('  - Training buffer: ${ocsvmStats['training_buffer_size']}');
      debugPrint('  - Is training: ${ocsvmStats['is_training']}');
      if (ocsvmStats['is_trained'] == true) {
        print('  - Accuracy: ${(ocsvmStats['accuracy'] * 100).toStringAsFixed(1)}%');
        print('  - Support vectors: ${ocsvmStats['support_vectors']}');
      }
      
      // Check if tracking is actually enabled and working
      _verifyTrackingStatus();
      
      // Run a test upload every 30 seconds (every 2nd debug cycle)
      if (timer.tick % 2 == 0) {
        print('[Dashboard] Running behavioral data recording test...');
        _testBehavioralDataRecording();
      }
    });
  }

  void _verifyTrackingStatus() async {
    print('[Dashboard] Verifying tracking status...');
    
    // Force a gyro burst and see if we get data
    GyroTracker().startBurst();
    
    // Wait for some data collection
    await Future.delayed(const Duration(milliseconds: 700));
    
    // Check if we collected any data
    final tempGyroData = GyroTracker().consumeMagnitudes();
    final tempHoldData = _holdAngleTracker.consumeHoldAngles();
    
    if (tempGyroData.isNotEmpty) {
      print('[Dashboard] ✅ Gyro tracking working - collected ${tempGyroData.length} samples');
    } else {
      print('[Dashboard] ❌ Gyro tracking not working - no samples collected');
      // Try to restart gyro tracking
      print('[Dashboard] Attempting to restart gyro tracking...');
      enableGyroTracking();
      GyroTracker().startPassive();
    }
    
    if (tempHoldData.isNotEmpty) {
      print('[Dashboard] ✅ Hold angle tracking working - collected ${tempHoldData.length} samples');
    } else {
      print('[Dashboard] ❌ Hold angle tracking not working - no samples collected');
      // Try to restart hold angle tracking
      print('[Dashboard] Attempting to restart hold angle tracking...');
      _holdAngleTracker.stop();
      await Future.delayed(const Duration(milliseconds: 100));
      _holdAngleTracker.start();
    }
  }



  // These methods are already defined above

  Future<void> _loadUserData() async {
    final userName = await AuthService.getCurrentUserName();
    if (mounted) {
      setState(() {
        _userName = userName;
      });
    }
  }

  Future<void> _loadDashboardData() async {
    try {
      await _transactionService.initialize();
      if (mounted) {
        setState(() {
          _currentBalance = _transactionService.getCurrentBalance();
          _recentTransactions = _transactionService.getRecentTransactions(limit: 4);
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading dashboard data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  double _calculateAverageSwipeVelocity() {
    if (_swipeVelocities.isEmpty) return 0.0;
    final total = _swipeVelocities.reduce((a, b) => a + b);
    return total / _swipeVelocities.length;
  }  Future<void> _recordBehavioralData() async {
    try {
      final avgVelocity = _calculateAverageSwipeVelocity();
      print('[Dashboard] avgSwipeVelocity: $avgVelocity');

      final sessionEndTime = DateTime.now();
      final sessionDuration = sessionEndTime.difference(_sessionStartTime!).inSeconds.toDouble();
      
      // Consume hold angles and gyro data
      final dashboardAngles = _holdAngleTracker.consumeHoldAngles();
      final gyroMagnitudes = GyroTracker().consumeMagnitudes();

      print('[Dashboard] Behavioral data summary:');
      print('  - Tap positions: ${_tapPositions.length}');
      print('  - Swipe velocities: ${_swipeVelocities.length}');
      print('  - Gyro magnitudes: ${gyroMagnitudes.length}');
      print('  - Hold angles: ${dashboardAngles.length}');
      print('  - Session duration: ${sessionDuration}s');

      final data = BehavioralData(
        tapPositions: _tapPositions.map((pos) => Offset(pos['x']!, pos['y']!)).toList(),
        swipeVelocities: _swipeVelocities,
        gyroMagnitudes: gyroMagnitudes,
        sessionDuration: sessionDuration,
        holdAngles: dashboardAngles,
      );
      
      // 🎯 OCSVM Integration: Add behavioral data for training/authentication
      try {
        if (_ocsvmService.isReady) {
          // Model is trained, perform authentication
          final ocsvmResult = await _ocsvmService.authenticate(data);
          print('[Dashboard] 🔐 OCSVM Authentication Result: $ocsvmResult');
          
          // You could use this result for additional security decisions
          if (!ocsvmResult.isAuthenticated && ocsvmResult.riskLevel == 'CRITICAL') {
            print('[Dashboard] ⚠️ CRITICAL: Behavioral anomaly detected!');
            // Could trigger additional security measures here
          }
        } else {
          // Model not trained yet, add data for training
          await _ocsvmService.addTrainingData(data);
          print('[Dashboard] 📚 Added behavioral data to OCSVM training buffer');
        }
      } catch (e) {
        print('[Dashboard] ❌ OCSVM error: $e');
      }
      
      final currentUser = AuthService.currentUserName ?? 'Unknown';
      await FirebaseService().uploadBehavioralData(currentUser, data);
      print('[Dashboard] ✅ Behavioral data uploaded for user: $currentUser');
      
    } catch (e) {
      print('[Dashboard] ❌ Error recording behavioral data: $e');
    }
  }

  // Method to manually trigger behavioral data recording for testing
  void _testBehavioralDataRecording() async {
    print('[Dashboard] Testing behavioral data recording...');
    
    // Add some test data if we don't have any
    if (_tapPositions.isEmpty) {
      _tapPositions.addAll([
        {'x': 100.0, 'y': 200.0},
        {'x': 150.0, 'y': 250.0},
      ]);
      print('[Dashboard] Added test tap positions');
    }
    
    if (_swipeVelocities.isEmpty) {
      _swipeVelocities.addAll([150.0, 200.0, 175.0]);
      print('[Dashboard] Added test swipe velocities');
    }
    
    // Force some gyro and hold angle data collection
    GyroTracker().startBurst();
    await Future.delayed(const Duration(milliseconds: 600));
    
    await _recordBehavioralData();
  }


  // These methods are already defined above

  }



  @override
  Widget build(BuildContext context) {
    return GestureDetector(
          onHorizontalDragStart: (details) {
            if (mounted) {
              setState(() {
                _swipeStart = details.globalPosition;
                _swipeStartTime = DateTime.now();
              });
            }
          },
          onHorizontalDragEnd: (details) {
            if (mounted && _swipeStartTime != null) {
              final velocity = details.primaryVelocity ?? 0.0;
              setState(() {
                _swipeVelocities.add(velocity.abs());
              });
            }
          },
          behavior: HitTestBehavior.translucent,
          onTapDown: (details) {
            if (mounted) {
              final Offset position = details.globalPosition;
              setState(() {
                _tapPositions[DateTime.now().millisecondsSinceEpoch] = position;
              });
            }
          },
      child: Scaffold(
        backgroundColor: constants.AppColors.bankingBackground,
        body: SafeArea(
          child: NotificationListener<ScrollNotification>(
            onNotification: (notification) {
              if (notification is ScrollUpdateNotification) {
                final double scrollDelta = notification.scrollDelta ?? 0.0;
                final Duration elapsed = const Duration(milliseconds: 16);
                final double velocity = scrollDelta / (elapsed.inMilliseconds / 1000);
                if (velocity.abs() > 0) {
                  _swipeVelocities.add(velocity.abs());
                  print('[Dashboard Scroll] Velocity recorded: $velocity');
                }
                
                // Trigger burst on scroll movement
                GyroTracker().startBurst();
              }
              return false;
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 24),
                  _buildBalanceCard(),
                  const SizedBox(height: 24),
                  _buildQuickActions(),
                  const SizedBox(height: 24),
                  _buildQuickSend(),
                  const SizedBox(height: 24),
                  _buildRecentTransactions(),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: _buildBottomNavigation(),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // User Avatar
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                constants.AppColors.bankingPrimary,
                constants.AppColors.bankingSecondary,
              ],
            ),
          ),
          child: const Icon(
            Icons.person,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),

        // Welcome Text
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Welcome back',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
              Text(
                _userName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // Emergency & Notification Icons
        Row(
          children: [
            // OCSVM Status Indicator
            _buildOCSVMStatusIndicator(),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: () => _showEmergencyOptions(),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFFDC2626),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.emergency,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: constants.AppColors.bankingSurface,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.notifications_outlined,
                color: Colors.white,
                size: 20,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOCSVMStatusIndicator() {
    return GestureDetector(
      onTap: () => _showOCSVMStatus(),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: _getOCSVMStatusColor().withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: _getOCSVMStatusColor(),
            width: 1,
          ),
        ),
        child: Icon(
          _getOCSVMStatusIcon(),
          color: _getOCSVMStatusColor(),
          size: 16,
        ),
      ),
    );
  }

  Color _getOCSVMStatusColor() {
    if (_ocsvmService.isTraining) return Colors.orange;
    if (_ocsvmService.isReady) return Colors.green;
    return Colors.grey;
  }

  IconData _getOCSVMStatusIcon() {
    if (_ocsvmService.isTraining) return Icons.psychology;
    if (_ocsvmService.isReady) return Icons.security;
    return Icons.pending;
  }

  void _showOCSVMStatus() {
    if (!mounted) return;
    
    final stats = _ocsvmService.getModelStats();
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) => AlertDialog(
        title: const Text('🧠 OCSVM Security Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Status: ${_ocsvmService.isReady ? "Ready" : _ocsvmService.isTraining ? "Training" : "Initializing"}'),
            const SizedBox(height: 8),
            Text('Training samples: ${stats['training_buffer_size']}'),
            if (stats['is_trained'] == true) ...[
              Text('Accuracy: ${(stats['accuracy'] * 100).toStringAsFixed(1)}%'),
              Text('Support vectors: ${stats['support_vectors']}'),
            ],
            const SizedBox(height: 8),
            Text('Privacy: ${_ocsvmService.privacyStatus}', 
                 style: const TextStyle(fontSize: 12, fontStyle: FontStyle.italic)),
          ],
        ),
        actions: [
          if (_ocsvmService.isReady)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _ocsvmService.forceRetrain();
              },
              child: const Text('Retrain'),
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            constants.AppColors.bankingPrimary,
            constants.AppColors.bankingPrimary.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: constants.AppColors.bankingPrimary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Your Balance',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                ),
              ),
              Text(
                _bankName,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          Text(
            _currentBalance.inr,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Card Number
          Row(
            children: [
              const Icon(
                Icons.credit_card,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _accountNumber,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              // Mastercard logo placeholder
              Container(
                width: 40,
                height: 24,
                decoration: BoxDecoration(
                  color: constants.AppColors.bankingAccent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Center(
                  child: Text(
                    'MC',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionButton(
              icon: Icons.send,
              label: 'Send',
              onTap: () {
                _showSendMoneyDialog();
              },
            ),
            _buildActionButton(
              icon: Icons.request_page,
              label: 'Request',
              onTap: () {
                _showRequestMoneyDialog();
              },
            ),
            _buildActionButton(
              icon: Icons.add_circle_outline,
              label: 'Top Up',
              onTap: () {
                _showTopUpDialog();
              },
            ),
          ],
        ),
        const SizedBox(height: 15),
        // 🚀 HACKATHON: ICP and Masumi Demo Button
        Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.icpMasumiDemo);
            },
            icon: const Icon(Icons.rocket_launch, color: Colors.white),
            label: const Text(
              '🚀 ICP & Masumi Integration Demo',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3B82F6),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        GyroTracker().startBurst(); // Trigger burst on tap
        onTap(); // Existing callback
        },
      child: Container(
        width: 80,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: constants.AppColors.bankingSurface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: constants.AppColors.bankingPrimary.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: constants.AppColors.bankingPrimary,
                size: 20,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickSend() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Quick Send',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to all contacts
              },
              child: Text(
                'See all',
                style: TextStyle(
                  color: constants.AppColors.bankingPrimary,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        SizedBox(
          height: 80,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              _buildContactAvatar('Add', Icons.add, isAddButton: true),
              _buildContactAvatar('Priya', Icons.person),
              _buildContactAvatar('Rahul', Icons.person),
              _buildContactAvatar('Anita', Icons.person),
              _buildContactAvatar('Vikram', Icons.person),
              _buildContactAvatar('Meera', Icons.person),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContactAvatar(String name, IconData icon, {bool isAddButton = false}) {
    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: GestureDetector(
        onTap: () {
          if (isAddButton) {
            // Navigate to add contact
          } else {
            // Navigate to send money to contact
          }
        },
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: isAddButton
                    ? constants.AppColors.bankingSurface
                    : constants.AppColors.bankingPrimary.withOpacity(0.2),
                shape: BoxShape.circle,
                border: isAddButton
                    ? Border.all(
                        color: constants.AppColors.bankingPrimary.withOpacity(0.3),
                        style: BorderStyle.solid,
                      )
                    : null,
              ),
              child: Icon(
                icon,
                color: isAddButton
                    ? constants.AppColors.bankingPrimary
                    : constants.AppColors.bankingPrimary,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              name,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Recent Transaction',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pushNamed(context, AppRoutes.activity);
              },
              child: Text(
                'See all',
                style: TextStyle(
                  color: constants.AppColors.bankingPrimary,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Dynamic transaction list
        if (_recentTransactions.isEmpty)
          const Center(
            child: Text(
              'No recent transactions',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
            ),
          )
        else
          ...(_recentTransactions.map((transaction) => Column(
            children: [
              _buildTransactionItem(
                title: transaction.title,
                subtitle: transaction.description,
                amount: transaction.amount,
                isCredit: transaction.isCredit,
                icon: transaction.icon,
                date: transaction.formattedDate,
              ),
              if (transaction != _recentTransactions.last)
                const SizedBox(height: 12),
            ],
          )).toList()),
      ],
    );
  }

  Widget _buildTransactionItem({
    required String title,
    required String subtitle,
    required double amount,
    required bool isCredit,
    required IconData icon,
    String? date,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isCredit
                  ? constants.AppColors.creditGreen.withOpacity(0.2)
                  : constants.AppColors.debitRed.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: isCredit
                  ? constants.AppColors.creditGreen
                  : constants.AppColors.debitRed,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 14,
                  ),
                ),
                if (date != null)
                  Text(
                    date!,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.4),
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ),

          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${isCredit ? '+' : '-'}${amount.inr}',
                style: TextStyle(
                  color: isCredit
                      ? constants.AppColors.creditGreen
                      : constants.AppColors.debitRed,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildNavItem(Icons.home, 'Home', true),
          _buildNavItem(Icons.bar_chart, 'Activity', false, onTap: () {
            Navigator.pushNamed(context, AppRoutes.activity);
          }),
          _buildNavItem(Icons.favorite_border, 'Favorites', false, onTap: () {
            _showFavoritesBottomSheet();
          }),
          _buildNavItem(Icons.person_outline, 'Profile', false, onTap: () {
            Navigator.pushNamed(context, AppRoutes.profile);
          }),
        ],
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, bool isActive, {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: () {
        GyroTracker().startBurst(); // Burst on nav icon tap
        if (onTap != null) onTap();
        },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: isActive
                ? constants.AppColors.bankingPrimary
                : Colors.white.withOpacity(0.5),
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: isActive
                  ? constants.AppColors.bankingPrimary
                  : Colors.white.withOpacity(0.5),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _showSendMoneyDialog() {
    GyroTracker().startBurst(); // Trigger burst sample
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.send, color: Color(0xFF3B82F6)),
              SizedBox(width: 8),
              Text(
                'Send Money',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Send Money feature will be available soon. You can transfer money to any UPI ID, bank account, or mobile number.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFF3B82F6)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showRequestMoneyDialog() {
    GyroTracker().startBurst();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.request_page, color: Color(0xFF10B981)),
              SizedBox(width: 8),
              Text(
                'Request Money',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Request Money feature will be available soon. You can request money from contacts via SMS, WhatsApp, or email.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFF10B981)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showTopUpDialog() {
    GyroTracker().startBurst();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.add_circle_outline, color: Color(0xFFFBBF24)),
              SizedBox(width: 8),
              Text(
                'Add Money',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Add Money feature will be available soon. You can add money from your linked bank accounts, debit cards, or UPI.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFFFBBF24)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showEmergencyOptions() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.emergency, color: Color(0xFFDC2626)),
              SizedBox(width: 8),
              Text(
                'Emergency Options',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildEmergencyOption(
                icon: Icons.block,
                title: 'Block Account',
                subtitle: 'Temporarily block all transactions',
                color: const Color(0xFFDC2626),
                onTap: () => _blockAccount(),
              ),
              const SizedBox(height: 12),
              _buildEmergencyOption(
                icon: Icons.phone,
                title: 'Emergency Contact',
                subtitle: 'Call bank emergency helpline',
                color: const Color(0xFFFBBF24),
                onTap: () => _callEmergencyHelpline(),
              ),
              const SizedBox(height: 12),
              _buildEmergencyOption(
                icon: Icons.security,
                title: 'Security Alert',
                subtitle: 'Report suspicious activity',
                color: const Color(0xFF3B82F6),
                onTap: () => _reportSuspiciousActivity(),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white70),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmergencyOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _blockAccount() {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.warning, color: Color(0xFFDC2626)),
              SizedBox(width: 8),
              Text(
                'Block Account',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Are you sure you want to block your account? This will prevent all transactions until you contact the bank.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _confirmAccountBlock();
              },
              child: const Text(
                'Block Account',
                style: TextStyle(color: Color(0xFFDC2626)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _confirmAccountBlock() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Account blocked successfully. Contact bank to unblock.'),
        backgroundColor: Color(0xFFDC2626),
      ),
    );
  }

  void _callEmergencyHelpline() {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Emergency Helpline: 1800-123-4567'),
        backgroundColor: Color(0xFFFBBF24),
      ),
    );
  }

  void _reportSuspiciousActivity() {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Security alert sent. Bank will contact you shortly.'),
        backgroundColor: Color(0xFF3B82F6),
      ),
    );
  }

  // Show favorites bottom sheet
  void _showFavoritesBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            const SizedBox(height: 12),
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Favorite Transactions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                children: [
                  _buildFavoriteItem(
                    'UPI to Priya Sharma',
                    'Rent Payment',
                    Icons.home,
                    '₹25,000',
                  ),
                  _buildFavoriteItem(
                    'Electricity Bill',
                    'MSEB Payment',
                    Icons.flash_on,
                    '₹3,200',
                  ),
                  _buildFavoriteItem(
                    'Amazon Purchase',
                    'Quick Shopping',
                    Icons.shopping_bag,
                    'Variable',
                  ),
                  _buildFavoriteItem(
                    'Petrol - Shell',
                    'Vehicle Fuel',
                    Icons.local_gas_station,
                    '₹2,000',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteItem(String title, String subtitle, IconData icon, String amount) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: constants.AppColors.bankingPrimary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: constants.AppColors.bankingPrimary,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Text(
            amount,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  void _startSecurityMonitoring() {
    _securityMonitor.startMonitoring(
      onRiskLevelChanged: _handleRiskLevelChange,
      onForceLogout: _handleForceLogout,
    );
  }

  void _handleRiskLevelChange(RiskLevel level, String message) {
    if (!mounted) return;

    switch (level) {
      case RiskLevel.low:
        // Normal operation, no action needed
        break;
      
      case RiskLevel.medium:
      case RiskLevel.moderate:
        if (!_showingBiometricPrompt) {
          _showingBiometricPrompt = true;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('⚠️ $message'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 5),
              onVisible: () {
                _showingBiometricPrompt = false;
              },
            ),
          );
        }
        break;
      
      case RiskLevel.high:
      case RiskLevel.critical:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('🚨 $message'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
        _handleForceLogout();
        break;
    }
  }

  void _handleForceLogout() {
    if (!mounted) return;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Security Alert'),
        content: const Text('Suspicious activity detected. You will be logged out for security reasons.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushReplacementNamed(AppRoutes.login);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    // Stop debug timer
    _debugTimer?.cancel();
    
    // Stop tracking and monitoring
    _securityMonitor.stopMonitoring();
    _holdAngleTracker.stop();
    GyroTracker().stopBurst();
    GyroTracker().stopPassive();  

    if (!_hasUploadedBehavioralData) {
      _recordBehavioralData();
      _hasUploadedBehavioralData = true;
  }

  super.dispose();
}

}