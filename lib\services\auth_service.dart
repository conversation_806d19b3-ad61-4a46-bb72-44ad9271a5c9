import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:math';
import 'package:trust_chain_banking/services/secure_storage_service.dart';

enum AuthState {
  firstLaunch,
  loggedOut,
  requiresReauth,
  requiresPinVerification,
  authenticated,
}

class AuthService {
  static const String _keyIsFirstLaunch = 'is_first_launch';
  static const String _keyUserToken = 'user_token';
  static const String _keyLastLoginTime = 'last_login_time';
  static const String _keyBehavioralData = 'behavioral_data';
  static String? currentUserName;
  static const String _keyUserProfile = 'user_profile';

  static SharedPreferences? _prefs;


  static Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  static Future<String> getCurrentUserName() async {
    await initialize();
    return currentUserName ?? 'Unknown';
  }

  static Future<AuthState> checkAuthenticationState() async {
    await initialize();
    
    // Check if first launch
    final isFirstLaunch = _prefs!.getBool(_keyIsFirstLaunch) ?? true;
    if (isFirstLaunch) {
      return AuthState.firstLaunch;
    }

    // Check if user has token
    final token = _prefs!.getString(_keyUserToken);
    if (token == null || token.isEmpty) {
      return AuthState.loggedOut;
    }

    // Check session validity
    final lastLoginTime = _prefs!.getInt(_keyLastLoginTime) ?? 0;
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    final sessionDuration = currentTime - lastLoginTime;
    
    // Session expires after 24 hours (86400000 ms)
    if (sessionDuration > 86400000) {
      return AuthState.requiresReauth;
    }

    // Check if requires PIN verification (after 15 minutes of inactivity)
    if (sessionDuration > 900000) {
      return AuthState.requiresPinVerification;
    }

    return AuthState.authenticated;
  }

  static Future<bool> login(String email, String password) async {
    await initialize();

    try {
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 1500));

      // Check if user exists (check if they've registered before)
      final existingProfile = await getUserProfile();

      if (existingProfile != null) {
        // User exists, validate credentials
        if (existingProfile['email'] == email && existingProfile['password'] == password) {
          currentUserName = email; // ✅ Store currently logged-in user

          // Valid credentials
          final token = _generateToken();
          final currentTime = DateTime.now().millisecondsSinceEpoch;

          // Store authentication data
          await _prefs!.setString(_keyUserToken, token);
          await _prefs!.setInt(_keyLastLoginTime, currentTime);
          await _prefs!.setBool(_keyIsFirstLaunch, false);

          // Update last login time in profile
          existingProfile['last_login'] = currentTime.toString();
          await _prefs!.setString(_keyUserProfile, jsonEncode(existingProfile));

          return true;
        } else {
          return false;
        }
      } else {
        // No user found - user needs to register first
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  static Future<bool> register(Map<String, dynamic> userData) async {
    await initialize();
    
    try {
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 2000));
      
      // Store registration data
      final token = _generateToken();
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      
      await _prefs!.setString(_keyUserToken, token);
      await _prefs!.setInt(_keyLastLoginTime, currentTime);
      await _prefs!.setBool(_keyIsFirstLaunch, false);
      
      // Ensure all user data values are strings
      final cleanUserData = <String, String>{};
      userData.forEach((key, value) {
        cleanUserData[key] = value.toString();
      });
      
      // Add last login as string
      cleanUserData['last_login'] = currentTime.toString();
      
      // Store user profile with clean data
      await _prefs!.setString(_keyUserProfile, jsonEncode(cleanUserData));
      AuthService.currentUserName = cleanUserData['email']; // ✅ set current user


      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<void> logout() async {
    await initialize();
    await _prefs!.remove(_keyUserToken);
    await _prefs!.remove(_keyLastLoginTime);
    await _prefs!.remove(_keyBehavioralData);
  }

  static Future<void> resetAllAuthData() async {
    await initialize();
    await _prefs!.clear(); // This clears ALL stored data
  }

  static Future<Map<String, dynamic>?> getUserProfile() async {
    await initialize();
    final profileJson = _prefs!.getString(_keyUserProfile);
    if (profileJson != null) {
      return jsonDecode(profileJson);
    }
    return null;
  }

  static Future<void> updateLastActivity() async {
    await initialize();
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    await _prefs!.setInt(_keyLastLoginTime, currentTime);
  }

  static Future<bool> verifyPin(String pin) async {
    await initialize();
    // For demo purposes, accept "1234" as valid PIN
    if (pin == "1234") {
      await updateLastActivity();
      return true;
    }
    return false;
  }

  static Future<void> setupPin(String pin) async {
    await initialize();
    // In a real app, this would be hashed and stored securely
    await _prefs!.setString('user_pin_hash', pin);
  }

  static Future<bool> hasPinSetup() async {
    await initialize();
    return _prefs!.containsKey('user_pin_hash');
  }

  static Future<void> storeBehavioralData(Map<String, dynamic> data) async {
    await initialize();
    final existingData = _prefs!.getString(_keyBehavioralData);
    List<Map<String, dynamic>> behavioralHistory = [];
    
    if (existingData != null) {
      final decoded = jsonDecode(existingData) as List;
      behavioralHistory = decoded.cast<Map<String, dynamic>>();
    }
    
    data['timestamp'] = DateTime.now().millisecondsSinceEpoch;
    behavioralHistory.add(data);
    
    // Keep only last 100 entries
    if (behavioralHistory.length > 100) {
      behavioralHistory = behavioralHistory.sublist(behavioralHistory.length - 100);
    }
    
    await _prefs!.setString(_keyBehavioralData, jsonEncode(behavioralHistory));
  }

  static Future<List<Map<String, dynamic>>> getBehavioralData() async {
    await initialize();
    final data = _prefs!.getString(_keyBehavioralData);
    if (data != null) {
      final decoded = jsonDecode(data) as List;
      return decoded.cast<Map<String, dynamic>>();
    }
    return [];
  }

  static String _generateToken() {
    final random = Random();
    final bytes = List<int>.generate(32, (_) => random.nextInt(256));
    return base64Url.encode(bytes);
  }
static Future<Map<String, String>?> getSavedCredentials() async {
  final storage = SecureStorageService();
  final email = await storage.read('user_email');
  final password = await storage.read('user_password');

  if (email != null && password != null) {
    return {
      'email': email,
      'password': password,
    };
  }
  return null;
}

  static Future<void> saveCredentials(String email, String password) async {
  await SecureStorageService().saveCredentials(email, password);
}

static Future<bool> loadCredentialsFromStorage() async {
  print('🔐 Attempting to auto-load credentials...');
  final creds = await getSavedCredentials();

  if (creds != null) {
    final email = creds['email']!;
    final password = creds['password']!;

    print('📥 Loaded email: $email');
    final loginSuccess = await login(email, password);

    if (loginSuccess) {
      print('✅ Auto-login successful');
      return true;
    } else {
      print('❌ Auto-login failed: Invalid credentials');
      return false;
    }
  } else {
    print('❌ No saved credentials found');
    return false;
  }
}


}
